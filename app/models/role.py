"""
Role and Permission models for RBAC system.
"""

from typing import List

from sqlalchemy import Column, Foreign<PERSON>ey, String, Table
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base

# Association table for many-to-many relationship between roles and permissions
role_permission_association = Table(
    "role_permissions",
    Base.metadata,
    Column("role_id", Foreign<PERSON>ey("roles.id"), primary_key=True),
    Column("permission_id", ForeignKey("permissions.id"), primary_key=True),
)

# Association table for many-to-many relationship between users and roles
user_role_association = Table(
    "user_roles",
    Base.metadata,
    Column("user_id", ForeignKey("users.id"), primary_key=True),
    Column("role_id", Foreign<PERSON>ey("roles.id"), primary_key=True),
)


class Permission(Base):
    """Permission model for fine-grained access control."""
    
    __tablename__ = "permissions"
    
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    description: Mapped[str] = mapped_column(String(255), nullable=True)
    resource: Mapped[str] = mapped_column(String(50), nullable=False)  # e.g., 'user', 'post', 'admin'
    action: Mapped[str] = mapped_column(String(50), nullable=False)    # e.g., 'read', 'write', 'delete'
    
    # Relationships
    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary=role_permission_association,
        back_populates="permissions"
    )
    
    def __repr__(self) -> str:
        return f"<Permission(name={self.name}, resource={self.resource}, action={self.action})>"


class Role(Base):
    """Role model for role-based access control."""
    
    __tablename__ = "roles"
    
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    description: Mapped[str] = mapped_column(String(255), nullable=True)
    is_default: Mapped[bool] = mapped_column(default=False)  # Default role for new users
    
    # Relationships
    permissions: Mapped[List[Permission]] = relationship(
        "Permission",
        secondary=role_permission_association,
        back_populates="roles"
    )
    users: Mapped[List["User"]] = relationship(
        "User",
        secondary=user_role_association,
        back_populates="roles"
    )
    
    def __repr__(self) -> str:
        return f"<Role(name={self.name})>"
    
    def has_permission(self, resource: str, action: str) -> bool:
        """Check if role has specific permission."""
        return any(
            perm.resource == resource and perm.action == action
            for perm in self.permissions
        )
    
    def get_permissions_list(self) -> List[str]:
        """Get list of permission names for this role."""
        return [f"{perm.resource}:{perm.action}" for perm in self.permissions]

"""
Common Pydantic schemas and base classes.
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, ConfigDict

DataType = TypeVar("DataType")


class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
    )


class TimestampMixin(BaseModel):
    """Mixin for models with timestamp fields."""
    
    created_at: datetime
    updated_at: datetime


class IDMixin(BaseModel):
    """Mixin for models with ID field."""
    
    id: int


class PaginationParams(BaseModel):
    """Pagination parameters for list endpoints."""
    
    skip: int = 0
    limit: int = 100
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "skip": 0,
                "limit": 100
            }
        }
    )


class PaginatedResponse(BaseSchema, Generic[DataType]):
    """Generic paginated response schema."""
    
    items: List[DataType]
    total: int
    skip: int
    limit: int
    has_next: bool
    has_prev: bool
    
    @classmethod
    def create(
        cls,
        items: List[DataType],
        total: int,
        skip: int = 0,
        limit: int = 100,
    ) -> "PaginatedResponse[DataType]":
        """Create paginated response."""
        return cls(
            items=items,
            total=total,
            skip=skip,
            limit=limit,
            has_next=skip + limit < total,
            has_prev=skip > 0,
        )


class SuccessResponse(BaseSchema):
    """Generic success response schema."""
    
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseSchema):
    """Generic error response schema."""
    
    success: bool = False
    error: str
    detail: Optional[str] = None
    errors: Optional[List[Dict[str, Any]]] = None


class HealthCheck(BaseSchema):
    """Health check response schema."""
    
    status: str
    timestamp: datetime
    version: str
    services: Dict[str, str]  # service_name -> status
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "healthy",
                "timestamp": "2024-01-01T00:00:00Z",
                "version": "1.0.0",
                "services": {
                    "database": "healthy",
                    "redis": "healthy",
                    "mongodb": "healthy",
                    "rabbitmq": "healthy"
                }
            }
        }
    )

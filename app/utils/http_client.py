"""
HTTP Client Utility Module

A comprehensive wrapper around aiohttp providing:
- Connection pooling and session management
- Robust error handling and detailed exceptions
- Exponential backoff retry mechanism
- Support for different authentication methods
- Content type handling (JSON, form, raw, etc.)
- Timeout and SSL configuration
- Response processing options

This utility can be used across the application to make HTTP requests with consistent
handling of errors, retries, and parsing.
"""

import asyncio
import json
import logging
from base64 import b64encode
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse, urljoin

import aiohttp
from aiohttp import (
    ClientConnectionError,
    ClientError,
    ClientPayloadError,
    ClientResponseError,
    ClientSession,
    ClientSSLError,
    ClientTimeout,
    ContentTypeError,
    TCPConnector,
)

from app.credential.utils.credential_manager import CredentialManager

logger = logging.getLogger(__name__)


class AuthType(str, Enum):
    """Authentication types supported by HttpClient"""
    NONE = "none"
    BASIC = "basic"
    BEARER = "bearer"
    API_KEY = "api_key"
    CUSTOM = "custom"


class ResponseFormat(str, Enum):
    """Response format options for HttpClient"""
    FULL = "full"  # Return complete response details
    BODY = "body"  # Return only response body (parsed JSON if possible)
    JSON = "json"  # Force JSON parse and return that
    TEXT = "text"  # Return text format
    STATUS = "status"  # Return only status code


class HttpMethod(str, Enum):
    """HTTP methods supported by HttpClient"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    PATCH = "PATCH"
    DELETE = "DELETE"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class HttpClientError(Exception):
    """Base exception class for HttpClient errors with detailed context"""

    def __init__(
        self, 
        message: str, 
        status_code: Optional[int] = None,
        url: Optional[str] = None,
        method: Optional[str] = None,
        response_data: Optional[Dict[str, Any]] = None,
        request_data: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.status_code = status_code
        self.url = url
        self.method = method
        self.response_data = response_data or {}
        self.request_data = request_data or {}
        self.cause = cause


class HttpClient:
    """
    Comprehensive HTTP client wrapper around aiohttp.
    
    This class provides connection pooling, retry mechanisms, authentication,
    and standardized error handling for HTTP requests.
    """

    # Class-level session for connection pooling
    _session: Optional[ClientSession] = None
    _default_timeout = 30  # Default timeout in seconds
    _session_timeout: Optional[ClientTimeout] = None

    def __init__(self):
        """Initialize the HTTP client."""
        pass

    @classmethod
    async def get_session(cls, timeout: Optional[int] = None) -> ClientSession:
        """
        Get or create a shared aiohttp session with connection pooling.
        
        Args:
            timeout: Request timeout in seconds (default: 30)
            
        Returns:
            ClientSession: The shared session instance
        """
        if cls._session is None or cls._session.closed:
            # Use the provided timeout or default
            timeout_value = timeout or cls._default_timeout
            
            # Create timeout configuration
            cls._session_timeout = ClientTimeout(
                total=timeout_value,
                connect=min(10, timeout_value / 2),  # Connect timeout shouldn't exceed half total timeout
                sock_connect=min(10, timeout_value / 2),  # Socket connect timeout
                sock_read=timeout_value  # Socket read timeout
            )

            # Create session with connection pooling
            connector = TCPConnector(
                limit=100,  # Total connection pool size
                limit_per_host=30,  # Per-host connection limit
                ttl_dns_cache=300,  # DNS cache TTL (seconds)
                use_dns_cache=True,
                keepalive_timeout=60,
                enable_cleanup_closed=True
            )

            cls._session = ClientSession(
                connector=connector,
                timeout=cls._session_timeout,
                headers={
                    'User-Agent': 'TemporalHttpClient/1.0'
                }
            )
            
            logger.debug("Created new aiohttp ClientSession with connection pooling")

        return cls._session

    @classmethod
    async def close_session(cls):
        """Close the shared session if it exists."""
        if cls._session and not cls._session.closed:
            await cls._session.close()
            cls._session = None
            logger.debug("Closed aiohttp ClientSession")

    @classmethod
    async def request(
        cls,
        method: Union[str, HttpMethod],
        url: str,
        *,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        auth: Optional[Dict[str, Any]] = None,
        timeout: Optional[int] = None,
        verify_ssl: bool = True,
        allow_redirects: bool = True,
        max_redirects: int = 10,
        proxy: Optional[str] = None,
        enable_retry: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        response_format: Union[str, ResponseFormat] = ResponseFormat.FULL,
        retryable_status_codes: Optional[List[int]] = None,
        credential: Optional[str] = None,
    ) -> Any:
        """
        Send an HTTP request with comprehensive error handling and retry logic.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            headers: Optional headers dictionary
            params: Optional query parameters dictionary
            json_data: Optional JSON data for request body
            data: Optional data for request body (form, text, etc.)
            auth: Authentication configuration
            timeout: Request timeout in seconds
            verify_ssl: Whether to verify SSL certificates
            allow_redirects: Whether to follow redirects
            max_redirects: Maximum number of redirects to follow
            proxy: Optional proxy URL
            enable_retry: Whether to enable retry logic
            max_retries: Maximum number of retries
            retry_delay: Initial delay between retries (increases exponentially)
            response_format: Format of the returned response
            retryable_status_codes: List of status codes that trigger a retry
            
        Returns:
            The response data in the requested format
            
        Raises:
            HttpClientError: For any request/response errors
        """
        print(f"Making HTTP request: {method} {url} with headers: {headers} and params: {params}")
        if isinstance(method, HttpMethod):
            method_str = method.value
        else:
            method_str = str(method).upper()
            
        if isinstance(response_format, ResponseFormat):
            response_format_str = response_format.value
        else:
            response_format_str = str(response_format).lower()
            
        # Default status codes that should be retried
        if retryable_status_codes is None:
            retryable_status_codes = [408, 429, 500, 502, 503, 504]

        # Prepare headers
        request_headers = {}
        if headers:
            request_headers.update(headers)
            
        # Process authentication if provided
        if auth:
            await cls._apply_authentication(auth, request_headers)
            
        # Handle credential-based authentication if provided
        request_url = url
        if credential:
            try:
                # Use CredentialManager to get authentication details
                credential_manager = CredentialManager()
                auth_details = await credential_manager.auth_request(credential, url)
                
                # Update URL with base URL from credential + endpoint
                request_url = urljoin(auth_details["base_url"], auth_details["endpoint"])
                
                # Update headers with credential headers
                if auth_details["headers"]:
                    request_headers.update(auth_details["headers"])
                    
                # Update query parameters with credential query params
                if auth_details["query_params"]:
                    if params is None:
                        params = {}
                    params.update(auth_details["query_params"])
                    
                logger.debug(f"Using credential '{credential}' for request to {url}")
            except Exception as e:
                logger.error(f"Error applying credential '{credential}': {str(e)}")
                raise HttpClientError(
                    message=f"Failed to apply credential '{credential}': {str(e)}",
                    url=url,
                    method=method_str,
                    cause=e
                )
            
        # Prepare request config
        config = {
            'headers': request_headers,
            'params': params,
            'ssl': verify_ssl,
            'allow_redirects': allow_redirects,
            'max_redirects': max_redirects,
            'proxy': proxy,
        }
        
        # Apply request body if provided
        if json_data is not None:
            config['json'] = json_data
            if 'Content-Type' not in request_headers:
                request_headers['Content-Type'] = 'application/json'
        elif data is not None:
            config['data'] = data

        # For logging/error contexts
        request_context = {
            'url': url,
            'method': method_str,
            'headers': {k: '***' if k.lower() in ['authorization', 'x-api-key'] else v for k, v in request_headers.items()},
            'params': params
        }
            
        # Get or create session
        session = await cls.get_session(timeout)
        
        # Apply retry logic
        last_error = None
        response = None
        
        actual_retries = max_retries if enable_retry else 0
        
        for attempt in range(actual_retries + 1):
            try:
                logger.debug(
                    f"HTTP {method_str} request to {request_url}" + 
                    (f" (attempt {attempt+1}/{actual_retries+1})" if attempt > 0 else "")
                )
                
                # Execute the request
                response = await session.request(method_str, request_url, **config)
                
                # Process response based on status code
                if response.status >= 400:
                    # Check if we should retry based on status code
                    should_retry = (
                        enable_retry and
                        attempt < actual_retries and
                        response.status in retryable_status_codes
                    )
                    
                    if should_retry:
                        # Read response content for logging/context
                        try:
                            response_text = await response.text()
                        except Exception:
                            response_text = ""
                            
                        logger.warning(
                            f"HTTP {method_str} request to {url} failed with status {response.status}, retrying..."
                        )
                        
                        # Calculate delay with exponential backoff
                        delay = retry_delay * (2 ** attempt)
                        await asyncio.sleep(delay)
                        continue
                    else:
                        # Process the error response
                        error_response = await cls._process_error_response(response)
                        raise HttpClientError(
                            message=f"HTTP {response.status}: {response.reason}",
                            status_code=response.status,
                            url=str(response.url),
                            method=method_str,
                            response_data=error_response,
                            request_data=request_context
                        )
                
                # Process successful response
                return await cls._process_response(response, response_format_str)

            except (ClientConnectionError, asyncio.TimeoutError) as e:
                last_error = e
                
                # Don't retry on the last attempt
                if not enable_retry or attempt >= actual_retries:
                    break
                    
                logger.warning(
                    f"Connection error during {method_str} request to {url}: {str(e)}, retrying..."
                )
                
                # Calculate delay with exponential backoff
                delay = retry_delay * (2 ** attempt)
                await asyncio.sleep(delay)
                
            except ClientResponseError as e:
                # These errors already handled above with custom error responses
                raise HttpClientError(
                    message=f"HTTP {e.status}: {e.message}",
                    status_code=e.status, 
                    url=url,
                    method=method_str,
                    request_data=request_context,
                    cause=e
                )
                
            except Exception as e:
                # Don't retry on unexpected errors
                raise HttpClientError(
                    message=f"Unexpected error during request: {str(e)}",
                    url=url,
                    method=method_str, 
                    request_data=request_context,
                    cause=e
                )
            finally:
                # Ensure response is closed if it was created but not processed
                if response is not None and not response.closed:
                    response.close()

        # If we get here, all retries failed
        raise HttpClientError(
            message=f"Request failed after {actual_retries + 1} attempts. Last error: {str(last_error)}",
            url=url,
            method=method_str,
            request_data=request_context,
            cause=last_error
        )

    @classmethod
    async def _apply_authentication(cls, auth: Dict[str, Any], headers: Dict[str, str]):
        """
        Apply authentication settings to request headers.
        
        Args:
            auth: Authentication configuration
            headers: Headers dictionary to update
        """
        auth_type = auth.get('type', 'none')
        
        if auth_type == AuthType.BASIC:
            username = auth.get('username', '')
            password = auth.get('password', '')
            
            if not username:
                raise ValueError("Username is required for basic authentication")
            
            # Create basic auth header
            credentials = f"{username}:{password}"
            encoded_credentials = b64encode(credentials.encode()).decode()
            headers['Authorization'] = f"Basic {encoded_credentials}"
            
        elif auth_type == AuthType.BEARER:
            token = auth.get('token')
            if not token:
                raise ValueError("Token is required for bearer authentication")
                
            headers['Authorization'] = f"Bearer {token}"
            
        elif auth_type == AuthType.API_KEY:
            token = auth.get('token')
            if not token:
                raise ValueError("Token is required for API key authentication")
                
            header_name = auth.get('header_name', 'X-API-Key')
            headers[header_name] = token
            
        elif auth_type == AuthType.CUSTOM:
            header_name = auth.get('header_name')
            header_value = auth.get('header_value')
            
            if not header_name or not header_value:
                raise ValueError("Header name and value are required for custom authentication")
                
            headers[header_name] = header_value

    @classmethod
    async def _process_error_response(cls, response) -> Dict[str, Any]:
        """
        Process an error HTTP response.
        
        Args:
            response: The aiohttp response object
            
        Returns:
            Dict containing error information
        """

        result = {
            'status_code': response.status,
            # 'headers': dict(response.headers),
        }

        try:
            # Try to read the response body as text
            body_text = await response.text()
            result['body'] = body_text
            
            # Try to parse as JSON
            try:
                json_data = json.loads(body_text)
                result['json'] = json_data
                
                # Extract error message from common fields
                if isinstance(json_data, dict):
                    for key in ['error', 'message', 'detail', 'description']:
                        if key in json_data:
                            error_detail = json_data.get(key)
                            if error_detail:
                                if isinstance(error_detail, str):
                                    result['error_detail'] = error_detail
                                else:
                                    result['error_detail'] = json.dumps(error_detail)
                                break
                                
            except json.JSONDecodeError:
                # Not JSON, that's fine
                pass
                
        except Exception as e:
            # Can't read response body
            result['body_error'] = str(e)
            
        return result

    @classmethod
    async def _process_response(cls, response, response_format: str) -> Any:
        """
        Process the HTTP response and format the result.
        
        Args:
            response: The aiohttp response object
            response_format: Format to return the response in
            
        Returns:
            The processed response data in the requested format
        """
        # Read response content
        try:
            response_text = await response.text()
        except Exception as e:
            response_text = f"<Error reading response text: {str(e)}>"

        # Try to parse JSON if needed
        response_json = None
        if response_text:
            try:
                response_json = json.loads(response_text)
            except json.JSONDecodeError:
                # Not JSON, that's fine for some response formats
                pass
        
        # Format response based on preference
        if response_format == ResponseFormat.BODY:
            return response_json if response_json is not None else response_text
            
        elif response_format == ResponseFormat.JSON:
            if response_json is None:
                raise HttpClientError(
                    message="Response is not valid JSON", 
                    status_code=response.status,
                    url=str(response.url),
                    method=response.method
                )
            return response_json
            
        elif response_format == ResponseFormat.TEXT:
            return response_text
            
        elif response_format == ResponseFormat.STATUS:
            return {'status_code': response.status}
            
        else:  # FULL by default
            result = {
                'status_code': response.status,
                'headers': dict(response.headers),
                'body': response_text,
                'url': str(response.url),
                'method': response.method,
                'ok': response.status < 400
            }

            if response_json is not None:
                result['json'] = response_json

            return result

    # Convenience methods for common HTTP methods
    @classmethod
    async def get(cls, url: str, *, credential: Optional[str] = None, **kwargs) -> Any:
        """
        Send HTTP GET request.
        
        Args:
            url: Request URL
            credential: Optional credential name to use for authentication
            **kwargs: Additional arguments for request method
            
        Returns:
            The response data in the requested format
        """
        return await cls.request(HttpMethod.GET, url, credential=credential, **kwargs)
        
    @classmethod
    async def post(cls, url: str, *, credential: Optional[str] = None, **kwargs) -> Any:
        """
        Send HTTP POST request.
        
        Args:
            url: Request URL
            credential: Optional credential name to use for authentication
            **kwargs: Additional arguments for request method
            
        Returns:
            The response data in the requested format
        """
        return await cls.request(HttpMethod.POST, url, credential=credential, **kwargs)
        
    @classmethod
    async def put(cls, url: str, *, credential: Optional[str] = None, **kwargs) -> Any:
        """
        Send HTTP PUT request.
        
        Args:
            url: Request URL
            credential: Optional credential name to use for authentication
            **kwargs: Additional arguments for request method
            
        Returns:
            The response data in the requested format
        """
        return await cls.request(HttpMethod.PUT, url, credential=credential, **kwargs)
        
    @classmethod
    async def patch(cls, url: str, *, credential: Optional[str] = None, **kwargs) -> Any:
        """
        Send HTTP PATCH request.
        
        Args:
            url: Request URL
            credential: Optional credential name to use for authentication
            **kwargs: Additional arguments for request method
            
        Returns:
            The response data in the requested format
        """
        return await cls.request(HttpMethod.PATCH, url, credential=credential, **kwargs)
        
    @classmethod
    async def delete(cls, url: str, *, credential: Optional[str] = None, **kwargs) -> Any:
        """
        Send HTTP DELETE request.
        
        Args:
            url: Request URL
            credential: Optional credential name to use for authentication
            **kwargs: Additional arguments for request method
            
        Returns:
            The response data in the requested format
        """
        return await cls.request(HttpMethod.DELETE, url, credential=credential, **kwargs)

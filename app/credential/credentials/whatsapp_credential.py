from app.credential.base.credential_model import <PERSON>th<PERSON>ethod, CredentialAuthModel, CredentialModel, CredentialTestModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter


@credential_provider(name="whatsapp_api")
class WhatsAppCredential(CredentialModel):
    name: str = "whatsapp_api"
    display_name: str = "WhatsApp API"
    description: str = "WhatsApp API credentials for sending messages"
    icon: str = "whatsapp"
    icon_color: str = "#25D366"
    icon_url: str = "https://example.com/icons/whatsapp.png"
    documentation_url: str = "https://developers.facebook.com/docs/whatsapp"
    subtitle: str = "WhatsApp API credentials"
    version: float = 1.0
    allowed_nodes: list[str] = ["whatsapp"]
    base_url: str = "https://graph.facebook.com/v12.0"
    parameters: list[NodeParameter] = [
        NodeParameter(
            name="access_token",
            display_name="Access Token",
            description="The access token for WhatsApp API",
            type="string",
            required=True,
            sensitive=True
        ),
        NodeParameter(
            name="business_id",
            display_name="Business Account ID",
            description="The ID of the WhatsApp business account",
            type="string",
            required=True
        )
    ]

    def authentication(self) -> CredentialAuthModel:
        return CredentialAuthModel(
            method=AuthMethod.BEARER,
            headers={"Authorization": f"Bearer {{access_token}}"},
            query_params={}
        )
    
    def test(self) -> CredentialTestModel:
        return CredentialTestModel(
            endpoint="/me",
            method="GET",
            validation=lambda response: 'id' in response and response['id'] is not None
        )
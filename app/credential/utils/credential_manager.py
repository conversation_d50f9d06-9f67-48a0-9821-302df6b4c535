from typing import Any, Dict
import re
import uuid

from app.core.security import decrypt_credential_data
from app.credential.base.credential_model import CredentialRequestModel
from app.credential.utils.credential_registry import CredentialRegistry
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.credential_repository import CredentialRepository


class CredentialManager:

    def __init__(self, db: AsyncSession = None):
        self.db = db

    def validate_credential(self, credential_request: CredentialRequestModel):
        credential = CredentialRegistry.get(credential_request.name)
        if not credential:
            raise ValueError(f"Credential type '{credential_request.name}' not found")
            
        for param in credential.parameters:
            # Check if required parameter is present
            if param.required and param.name not in credential_request.parameters:
                raise ValueError(f"Missing required parameter: {param.name}")
                
            # If parameter is present, validate its type
            if param.name in credential_request.parameters:
                value = credential_request.parameters[param.name]
                
                # Validate type based on NodePropertyTypes enum
                self._validate_param_type(param.name, value, param.type)
                
        return True
    
    async def test_credential(self, id: uuid.UUID):
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")
        try:
            credential_data = decrypt_credential_data(credential.data)
            test_config = credential_details.test()
            url = credential_details.base_url + test_config.endpoint
            auth = credential_details.authentication()
            headers = self._get_placeholder_values(auth.headers, credential_data)
            query_params = self._get_placeholder_values(auth.query_params, credential_data)

            from app.utils.http_client import HttpClient, HttpClientError
            response = await HttpClient.request(
                method=test_config.method,
                url=url,
                headers=headers,
                params=query_params,
                data=test_config.body
            )

            if not test_config.validation(response["json"]):
                raise ValueError("Credential test failed: Invalid response data.")
            
            if credential.status != "active":
                await credential_repo.update(credential, {"status": "active"})
                await self.db.commit()
            return True
        except HttpClientError as e:
            error_msg = str(e)
            if hasattr(e, 'status_code') and e.status_code == 401:
                error_msg += " - Check if you're providing all required parameters for this API"
            raise ValueError(f"Failed to test credential: {error_msg}")

    def _get_placeholder_values(self, placeholder_dict: Dict[str, str], params: Dict[str, str]) -> dict[str, str]:
        result = placeholder_dict.copy()
        for key, value in result.items():
            # Find all placeholders like {access_token} within the header value
            placeholders = re.findall(r"{(.*?)}", value)

            # Replace each placeholder with its corresponding value from params
            for placeholder in placeholders:
                if placeholder in params:
                    value = value.replace(f"{{{placeholder}}}", str(params[placeholder]["value"]))
                else:
                    raise ValueError(f"Missing value for placeholder: {placeholder}")
            
            # Update the header with the replaced value
            result[key] = value

        return result
    
    async def auth_request(self, credential_name: str, url: str) -> Dict[str, Any]:
        """
        Authenticate a request using a credential.
        
        Args:
            credential_name: Name of the credential to use
            url: The original URL to make the request to
            
        Returns:
            Dict containing base_url, headers and query_params
        """
        credential = CredentialRegistry.get(credential_name)
        if not credential:
            raise ValueError(f"Credential with name {credential_name} not found.")
        
        # Extract endpoint from the original URL
        original_url_parts = url.split("://", 1)
        if len(original_url_parts) > 1:
            _, path_and_query = original_url_parts
            path_parts = path_and_query.split("/", 1)
            endpoint = "/" + path_parts[1] if len(path_parts) > 1 else "/"
        else:
            endpoint = "/"
            
        # Get authentication headers
        auth = credential.authentication()
        headers = self._get_header(auth.headers, credential.parameters)
        
        # Get query parameters
        query_params = self._get_query_params(credential.parameters)
        
        return {
            "base_url": credential.base_url,
            "endpoint": endpoint,
            "headers": headers,
            "query_params": query_params
        }
    
    def _validate_param_type(self, param_name: str, value: Any, param_type_enum: str) -> None:
        """
        Validate that a parameter value matches the expected type from NodePropertyTypes enum.
        
        Args:
            param_name: The name of the parameter being validated
            value: The value to validate
            param_type_enum: The NodePropertyTypes enum value
            
        Raises:
            ValueError: If the value doesn't match the expected type
        """
        from app.node.node_base.node_models import NodePropertyTypes
        
        # Handle validation based on the type specified in NodePropertyTypes enum
        if param_type_enum == NodePropertyTypes.STRING:
            if not isinstance(value, str):
                raise ValueError(f"Parameter '{param_name}' must be a string, got {type(value).__name__}")
                
        elif param_type_enum == NodePropertyTypes.NUMBER:
            if not isinstance(value, (int, float)):
                raise ValueError(f"Parameter '{param_name}' must be a number, got {type(value).__name__}")
                
        elif param_type_enum == NodePropertyTypes.BOOLEAN:
            if not isinstance(value, bool):
                raise ValueError(f"Parameter '{param_name}' must be a boolean, got {type(value).__name__}")
                
        elif param_type_enum == NodePropertyTypes.JSON:
            if not isinstance(value, (dict, list)):
                raise ValueError(f"Parameter '{param_name}' must be a JSON object or array, got {type(value).__name__}")
        
        elif param_type_enum == NodePropertyTypes.OPTIONS or param_type_enum == NodePropertyTypes.MULTI_OPTIONS:
            # These are typically string values selected from options
            if not isinstance(value, str) and not (isinstance(value, list) and all(isinstance(item, str) for item in value)):
                raise ValueError(f"Parameter '{param_name}' must be a string or list of strings, got {type(value).__name__}")
        
        # Add more type validations as needed for other NodePropertyTypes
        # For now, other types will pass validation
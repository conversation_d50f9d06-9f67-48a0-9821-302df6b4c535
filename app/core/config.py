"""
Application configuration using Pydantic Settings.
Supports multiple environments with type-safe configuration.
"""

from functools import lru_cache
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, PostgresDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment-specific configurations."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )
    
    # Application
    APP_NAME: str = "Cerebro API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database - PostgreSQL
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "cerebro_user"
    POSTGRES_PASSWORD: str = "cerebro_password"
    POSTGRES_DB: str = "cerebro"
    POSTGRES_PORT: int = 5432
    DATABASE_URL: Optional[PostgresDsn] = None
    
    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> Any:
        if isinstance(v, str):
            return v
        values = info.data if hasattr(info, 'data') else {}
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            username=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            port=values.get("POSTGRES_PORT"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
    
    # MongoDB
    MONGODB_URL: str = "***************************************************************"
    MONGODB_DB_NAME: str = "cerebro"
    
    # Redis
    REDIS_URL: str = "redis://:cerebro_password@localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = "cerebro_password"
    REDIS_DB: int = 0
    
    # RabbitMQ
    RABBITMQ_URL: str = "amqp://cerebro_user:cerebro_password@localhost:5672/cerebro"
    RABBITMQ_HOST: str = "localhost"
    RABBITMQ_PORT: int = 5672
    RABBITMQ_USER: str = "cerebro_user"
    RABBITMQ_PASSWORD: str = "cerebro_password"
    RABBITMQ_VHOST: str = "cerebro"
    
    # Inngest
    INNGEST_URL: str = "http://localhost:8288"
    INNGEST_EVENT_KEY: str = "local"
    INNGEST_SIGNING_KEY: str = "signkey-test-12345678901234567890123456789012"
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Testing
    TESTING: bool = False
    TEST_DATABASE_URL: Optional[str] = None
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds
    
    # Email (for notifications)
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # Superuser
    FIRST_SUPERUSER_EMAIL: str = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "Admin123!"

    # Temporal
    TEMPORAL_HOST: str = "localhost"
    TEMPORAL_PORT: int = 7234
    TEMPORAL_NAMESPACE: str = "default"

    # Credentials
    CREDENTIAL_MASTER_KEY: str = "your-secret-key-change-this-in-production"



class TestSettings(Settings):
    """Test environment specific settings."""
    
    TESTING: bool = True
    DATABASE_URL: str = "postgresql+asyncpg://test_user:test_password@localhost:5432/test_cerebro"
    MONGODB_URL: str = "**************************************************************"
    REDIS_URL: str = "redis://:test_password@localhost:6379/1"
    
    # Disable external services in tests
    RATE_LIMIT_ENABLED: bool = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


@lru_cache()
def get_test_settings() -> TestSettings:
    """Get cached test settings."""
    return TestSettings()


# Global settings instance
settings = get_settings()

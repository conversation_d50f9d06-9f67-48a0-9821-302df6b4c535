from fastapi import APIRouter
from fastapi.params import Depends
from app.node.node_utils.registry import NodeRegistry, node_registry
from app.schemas.common import PaginatedResponse, PaginationParams

router = APIRouter()

@router.get("/", response_model=PaginatedResponse[dict])
async def get_nodes(
        pagination: PaginationParams = Depends()
):
    nodes: list[dict] = []

    try:
        # Get registered node descriptions
        descriptions = node_registry.list_node_descriptions()
        
        # Use the to_api_format method to properly handle child class properties
        for desc in descriptions:
            nodes.append(desc.dict())

    except Exception as e:
        pass

    return PaginatedResponse.create(
            items=nodes,
            total=len(nodes),
            skip=pagination.skip,
            limit=pagination.limit
        )
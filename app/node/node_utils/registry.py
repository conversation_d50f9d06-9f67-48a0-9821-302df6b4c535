"""
Node Registry

This module provides a registry for all node types available in the application.
It allows for node discovery, registration, and searching.
"""

from typing import Dict, List, Optional, Type, Any
import re
from fuzzywuzzy import fuzz, process

from typing import Dict, Any, Type

from typing import Dict, Any, Type

from app.node.node_base.node import Node
from app.node.node_base.node_models import NodeTypeDescription

class NodeRegistry:
    """    A singleton registry for managing node types in the application.
    This class allows for registering, retrieving, and searching nodes by type.
    It supports fuzzy searching to handle typos and similar terms.
    """

    def __init__(self):
        self._registry = {}

    # @classmethod
    def register_node(self, node_type: str, node_class: Type[Node]) -> None:
        """
        Register a node class with its node type in the registry.
        
        Args:
            node_type (str): The type of the node (e.g., 'if', 'delay').
            node_class (type): The class of the node.
        """
        self._registry[node_type] = node_class
        
    def get_node_class(self, node_type: str) -> Optional[Type[Node]]:
        """
        Get a node class by its type.
        
        Args:
            node_type: The node type identifier
            
        Returns:
            The node class if found, None otherwise
        """
        return self._registry.get(node_type, None)
        
    def get_activity_node_class(self) -> List[Type[Node]]:
        nodes = []
        for node_class in self._registry.values():
            if hasattr(node_class, 'is_activity') and node_class.is_activity:
                nodes.append(node_class)
        return nodes

    def list_node_descriptions(self) -> List[NodeTypeDescription]:
        """
        Get a list of all node type descriptions.
        
        Returns:
            List of node type descriptions (preserving all properties from child classes)
        """
        descriptions = []
        
        for node_class in self._registry.values():
            # Get the description from the node class
            # This will include all properties from child classes
            description = node_class.get_description()
            descriptions.append(description)
        
        return descriptions
    
    def search_nodes(self, query: str, threshold: float = 60) -> List[Dict[str, Any]]:
        """
        Search for nodes by name, type, or description.
        Uses fuzzy matching to handle typos and similar words.
        
        Args:
            query: Search query
            threshold: Minimum similarity threshold (0-100) for fuzzy matching
            
        Returns:
            List of nodes that match the search query with similarity scores
        """
        if not query:
            # Return all nodes if no query is provided
            return [
                {
                    **desc.to_api_format(),  # Use to_api_format to include all properties
                    "score": 100,  # Perfect match score
                } for desc in self.list_node_descriptions()
            ]
        
        # Clean query
        query = query.lower().strip()
        
        results = []
        for desc in self.list_node_descriptions():
            # Create searchable texts from different node properties
            name_text = desc.name.lower()
            display_text = desc.display_name.lower()
            desc_text = desc.description.lower()
            
            # Calculate different similarity scores using different algorithms
            # These give better results for different kinds of typos and search patterns
            name_ratio = fuzz.ratio(query, name_text)
            name_partial = fuzz.partial_ratio(query, name_text)
            display_ratio = fuzz.ratio(query, display_text) 
            display_partial = fuzz.partial_ratio(query, display_text)
            desc_partial = fuzz.partial_ratio(query, desc_text)
            
            # Token sort ratio helps when word order is different
            name_sort = fuzz.token_sort_ratio(query, name_text)
            display_sort = fuzz.token_sort_ratio(query, display_text)
            
            # Use the best score from all methods
            best_score = max(name_ratio, name_partial, display_ratio, 
                             display_partial, desc_partial, name_sort, display_sort)
            
            # Normalize score to 0-1 range for API consistency
            normalized_score = best_score / 100.0
            
            # Include node if score exceeds threshold
            if best_score >= threshold:
                # Use to_api_format to include all properties from child classes
                node_data = desc.to_api_format()
                node_data["score"] = normalized_score
                results.append(node_data)
        
        # Sort results by score (highest first)
        return sorted(results, key=lambda x: x["score"], reverse=True)


# Create a singleton instance
node_registry = NodeRegistry()
    
    
# Export the registry instance as the public API
__all__ = ['node_registry']

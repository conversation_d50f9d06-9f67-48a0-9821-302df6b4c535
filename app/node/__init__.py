"""
Nodes Package

This package contains the base Node class, node-related models, implementations
of various node types, and the node registry for discovering and searching nodes.
"""

import importlib
import os
from pathlib import Path
from app.node.node_base.node import Node
from app.node.nodes.wait.wait_node import WaitNode
from app.node.nodes.http_request.http_request_node import HTTPRequestNode
from app.node.node_base.node_models import (
    # Type definitions
    NodeParameterValue,
    NodeGroupType,
    NodeConnectionType,
    
    # Enums
    NodePropertyTypes,
    NodePropertyAction,
    CodeAutocompleteTypes,
    EditorType,
    SQLDialect,
    
    # Models
    NodeTypeDescription,
    NodeParameter,
    NodeParameterOption,
    NodeParameterValueType,
    NodeData,
    NodeConnection,
    NodeCredentialDescription,
    
    # UI models
    NodePropertyTypeOptions,
    CalloutAction,
    DisplayOptions,
    DisplayCondition,
    CredentialsDisplayOptions,
)

__all__ = [
    # Base classes
    'Node',
    
    # Node implementations
    'WaitNode',
    'HTTPRequestNode',
    
    # Registry
    # 'node_registry',
    # 'register_builtin_nodes',
    
    # Type definitions and models
    'NodeParameterValue',
    'NodeGroupType',
    'NodeConnectionType',
    'NodePropertyTypes',
    'NodePropertyAction',
    'CodeAutocompleteTypes',
    'EditorType',
    'SQLDialect',
    'NodeTypeDescription',
    'NodeParameter',
    'NodeParameterOption',
    'NodeParameterValueType',
    'NodeData',
    'NodeConnection',
    'NodeCredentialDescription',
    'NodePropertyTypeOptions',
    'CalloutAction',
    'DisplayOptions',
    'DisplayCondition',
    'CredentialsDisplayOptions',
]

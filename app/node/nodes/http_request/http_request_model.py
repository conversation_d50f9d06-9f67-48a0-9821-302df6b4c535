from typing import ClassVar
from app.node.node_base.node_models import NodeConnectionType, NodeParameter, NodeParameterOption, NodePropertyTypes, NodeTypeDescription


class HTTPRequestNodeDescription(NodeTypeDescription):
    """
    Description for the HTTP Request Node.

    This node sends a HTTP request to a specified URL.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "http_request"

    @classmethod
    def create(cls) -> "HTTPRequestNodeDescription":
        """
        Factory method to create a standard HTTP request node description.

        Returns:
            HTTPRequestNodeDescription: A configured description for HTTP request nodes
        """
        return cls(
            name="http_request",
            display_name="HTTP Request",
            description="Sends a HTTP request to a specified URL.",
            icon="globe",
            icon_color="#408050",
            group=["organization"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="url",
                    display_name="URL",
                    description="The URL to send the webhook request to",
                    type=NodePropertyTypes.STRING,
                    default="",
                    required=True
                ),
                NodeParameter(
                    name="method",
                    display_name="HTTP Method",
                    description="The HTTP method to use for the request",
                    type=NodePropertyTypes.OPTIONS,
                    default="POST",
                    required=True,
                    options=[
                        NodeParameterOption(name="GET", value="GET"),
                        NodeParameterOption(name="POST", value="POST"),
                        NodeParameterOption(name="PUT", value="PUT"),
                        NodeParameterOption(name="DELETE", value="DELETE")
                    ]
                ),
                NodeParameter(
                    name="headers",
                    display_name="Headers",
                    description="HTTP headers to include in the request",
                    type=NodePropertyTypes.JSON,
                    default={},
                    required=False
                ),
                NodeParameter(
                    name="payload",
                    display_name="Payload",
                    description="Data to send in the request body",
                    type=NodePropertyTypes.JSON,
                    default={},
                    required=False
                )
            ]
        )
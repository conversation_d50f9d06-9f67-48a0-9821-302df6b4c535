from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.http_request.http_request_model import HTTPRequestNodeDescription
import json

@node_defn(type='http_request', is_activity=True)
class HTTPRequestNode(Node):
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        return HTTPRequestNodeDescription.create()

    def __init__(self, description: NodeTypeDescription = None):
        if not description:
            description = self.get_description()
        super().__init__(description)
    
    async def run(self, data: NodeData) -> NodeResult:
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid data")

        # Extract parameters
        url = self.data.parameters.get('url')
        if not url:
            return NodeResult(error="URL is required")
            
        method = self.data.parameters.get('method', 'GET')
        headers = self.data.parameters.get('headers', {})
        payload = self.data.parameters.get('payload', {})
        
        # Handle string format for JSON parameters
        if isinstance(headers, str):
            try:
                headers = json.loads(headers)
            except json.JSONDecodeError:
                return NodeResult(error="Invalid JSON format for headers")
                
        if isinstance(payload, str):
            try:
                payload = json.loads(payload)
            except json.JSONDecodeError:
                return NodeResult(error="Invalid JSON format for payload")
        
        # Make the request - Fixed to use correct method and parameters
        try:
            import requests
            response = requests.request(
                method=method,  # Use the actual method from parameters
                url=url,
                headers=headers,
                json=payload if method in ['POST', 'PUT', 'PATCH'] else None,
                params=payload if method == 'GET' else None,
                timeout=30  # Set a reasonable timeout
            )
            
            # Prepare response data
            result = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text
            }
            
            # Try to parse JSON if possible
            try:
                result["json"] = response.json()
            except:
                pass  # Not JSON content, that's fine
                
            return NodeResult(
                result=result,
                next_connection_index=0
            )
            
        except requests.RequestException as e:
            return NodeResult(error=f"Request failed: {str(e)}")
            
        except Exception as e:
            return NodeResult(error=f"Webhook execution error: {str(e)}")
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        return self.base_validate(request)

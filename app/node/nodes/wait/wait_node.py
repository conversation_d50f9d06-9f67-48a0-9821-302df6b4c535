"""
Wait Node Implementation

This module implements the wait node functionality for pausing workflow execution.
"""

import asyncio
from datetime import datetime, timedelta

from app.node.node_base.node import Node, NodeResult
from app.node.node_utils.workflow_defn import node_defn

from app.node.node_base.node_models import (
    NodeData, 
    NodeTypeDescription, 
    NodeRequest, 
    ValidationResult,
    ValidationError
)
from temporalio import workflow
from app.node.nodes.wait.wait_model import WaitNodeDescription


@node_defn(type='wait', is_activity=False)
class WaitNode(Node):
    """
    Node that pauses workflow execution for a specified duration or until a specific time.
    
    This node supports:
    - Waiting for a specified time interval (seconds, minutes, hours, days)
    - Waiting until a specific date and time
    - Enforcing a maximum wait time of 1 year
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """
        Get the description for this node type.
        
        Returns:
            NodeTypeDescription: The node type description
        """
        return WaitNodeDescription.create()
    
    def __init__(self, description: NodeTypeDescription = None):
        """
        Initialize a wait node.
        
        Args:
            description: <PERSON><PERSON><PERSON> describing this node type's capabilities
        """
        if not description:
            description = self.get_description()
        super().__init__(description)
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the wait node's logic by pausing execution for the configured duration.
        
        Returns:
            Any: Input data passed through after the wait completes
        
        Raises:
            ValueError: If parameters are missing or invalid
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return self.error("Node data or parameters not set")

        # Get resume type - parameters in the NodeData are still stored as a dictionary for runtime values
        resume_type = str(self.data.parameters.get("resume_type", "time_interval"))
        
        # Handle waiting based on resume type
        if resume_type == "time_interval":
            # Make sure required parameters exist
            if "wait_value" not in self.data.parameters:
                raise ValueError("Wait Amount is required for time interval mode")
            if "wait_unit" not in self.data.parameters:
                raise ValueError("Wait Unit is required for time interval mode")

            result = await self._wait_for_time_interval()
        elif resume_type == "specific_time":
            # Make sure required parameters exist
            if "date_time" not in self.data.parameters:
                raise ValueError("Date and Time is required for specific time mode")
                
            result = await self._wait_until_specific_time()
        else:
            raise ValueError(f"Unknown resume type: {resume_type}")
        
        return NodeResult(result=result, next_connection_index=0, error=None)
    
    async def _wait_for_time_interval(self) -> None:
        """
        Wait for a specified time interval.
        
        Raises:
            ValueError: If the parameters are invalid or wait duration exceeds 1 year
        """
        # Get wait parameters
        wait_value = float(self.data.parameters.get("wait_value", 5))
        wait_unit = str(self.data.parameters.get("wait_unit", "seconds"))
        
        # Calculate wait in seconds
        wait_seconds = self._convert_to_seconds(wait_value, wait_unit)
        
        # Check if wait exceeds maximum of 1 year
        max_wait = self._convert_to_seconds(1, "days") * 365  # 1 year in seconds
        if wait_seconds > max_wait:
            raise ValueError(f"Wait duration exceeds maximum allowed (1 year): {wait_value} {wait_unit}")

        # Execute the wait
        await asyncio.sleep(wait_seconds)
        return {"wait_duration": wait_seconds, "wait_unit": wait_unit}
    
    async def _wait_until_specific_time(self) -> None:
        """
        Wait until a specific date and time.
        
        Raises:
            ValueError: If the date/time is invalid or more than 1 year in the future
        """
        date_time = self.data.parameters.get("date_time")
        if not date_time:
            raise ValueError("No date/time specified for specific time wait")

        # Parse the date/time
        try:
            if isinstance(date_time, str):
                # Try to parse ISO format
                try:
                    target_time = datetime.fromisoformat(date_time)
                except ValueError:
                    # Some date formats include 'Z' at the end which fromisoformat doesn't handle before Python 3.11
                    # Try to handle common formats
                    if date_time.endswith('Z'):
                        date_time = date_time[:-1] + '+00:00'
                        target_time = datetime.fromisoformat(date_time)
                    else:
                        raise ValueError(f"Invalid date/time format: {date_time}")
            elif isinstance(date_time, datetime):
                target_time = date_time
            else:
                raise ValueError(f"Invalid date/time format: {type(date_time)}")
        except Exception as e:
            raise ValueError(f"Failed to parse date/time '{date_time}': {str(e)}")

        # Calculate seconds to wait
        now = workflow.now()
        if target_time < now:
            # If time is in the past, don't wait
            return {"wait_until": target_time.isoformat(), "wait_duration": 0}
            
        # Check if wait exceeds maximum of 1 year
        max_wait_time = now + timedelta(days=365)
        if target_time > max_wait_time:
            raise ValueError("Wait time exceeds maximum allowed (1 year)")

        # Calculate seconds to wait
        wait_seconds = (target_time - now).total_seconds()
        
        # Execute the wait
        await asyncio.sleep(wait_seconds)
        return {"wait_until": target_time.isoformat(), "wait_duration": wait_seconds}
    
    @staticmethod
    def _convert_to_seconds(value: float, unit: str) -> float:
        """
        Convert a duration value to seconds based on the unit.
        
        Args:
            value: The duration value
            unit: The time unit (seconds, minutes, hours, days)
            
        Returns:
            float: The equivalent duration in seconds
            
        Raises:
            ValueError: If the unit is unknown
        """
        if unit == "seconds":
            return value
        elif unit == "minutes":
            return value * 60
        elif unit == "hours":
            return value * 3600  # 60 * 60
        elif unit == "days":
            return value * 86400  # 24 * 60 * 60
        else:
            raise ValueError(f"Unknown duration unit: {unit}")
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a wait node request.
        
        Performs node-specific validation for wait nodes:
        - For time_interval mode: validates wait_value and wait_unit
        - For specific_time mode: validates date_time
        - Enforces maximum wait time of 1 year
        
        Args:
            request: The node request to validate
            
        Returns:
            ValidationResult: The validation result with any errors found
        """
        # Run base validation first
        result = self.base_validate(request)
        
        # If base validation failed, no need to continue
        if not result.valid:
            return result
            
        errors = result.errors or []
        parameters = request.parameters
        
        # Get the resume type
        resume_type = str(parameters.get("resume_type", "time_interval"))
        
        if resume_type == "time_interval":
            # Validate wait value is present and positive
            # if "wait_value" in parameters:
            try:
                wait_value = float(parameters["wait_value"])
                if wait_value <= 0:
                    errors.append(ValidationError(
                        parameter="wait_value",
                        message="Wait amount must be positive"
                    ))
            except (ValueError, TypeError):
                errors.append(ValidationError(
                    parameter="wait_value",
                    message="Wait amount must be a number"
                ))
                    
            # Validate wait unit
            wait_unit = parameters["wait_unit"]
            valid_units = ["seconds", "minutes", "hours", "days"]
            if wait_unit not in valid_units:
                errors.append(ValidationError(
                    parameter="wait_unit",
                    message=f"Wait unit must be one of: {', '.join(valid_units)}"
                ))
                    
                    
        elif resume_type == "specific_time":
            # Validate date_time
            
            date_time = parameters["date_time"]
            
            if not date_time:
                errors.append(ValidationError(
                    parameter="date_time",
                    message="Date and time is required"
                ))
            else:
                try:
                    # Try to parse the date_time
                    from datetime import datetime, timezone
                    
                    if isinstance(date_time, str):
                        # Try to parse ISO format
                        try:
                            target_time = datetime.fromisoformat(date_time)
                        except ValueError:
                            # Some date formats include 'Z' at the end which fromisoformat doesn't handle before Python 3.11
                            # Try to handle common formats
                            if date_time.endswith('Z'):
                                date_time = date_time[:-1] + '+00:00'
                                target_time = datetime.fromisoformat(date_time)
                            else:
                                raise
                    elif isinstance(date_time, datetime):
                        target_time = date_time
                    else:
                        raise ValueError(f"Invalid date/time format: {type(date_time)}")
                        
                    # Check if date is in the past
                    now = datetime.now(timezone.utc)
                    if target_time < now:
                        errors.append(ValidationError(
                            parameter="date_time",
                            message="Date and time cannot be in the past"
                        ))
                        
                    
                except Exception as e:
                    errors.append(ValidationError(
                        parameter="date_time",
                        message=f"Invalid date and time format: {str(e)}"
                    ))
            
        else:
            errors.append(ValidationError(
                parameter="resume_type",
                message=f"Unknown resume type: {resume_type}"
            ))
            
        return ValidationResult(valid=len(errors) == 0, errors=errors)

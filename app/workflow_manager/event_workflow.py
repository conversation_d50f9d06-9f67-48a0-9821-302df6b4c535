from temporalio import workflow

from app.node.node_base.node_models import WorkflowModel
from .workflow_executor import WorkflowExecutor

@workflow.defn(name="event_workflow")
class EventWorkflow:

    def __init__(self):
        self.workflowExecutor = None

    @workflow.run
    async def execute(self, workflow_input: WorkflowModel):
        self.workflowExecutor = WorkflowExecutor(workflow_input)
        return await self.workflowExecutor.run()

    @workflow.query(name="get_event_workflow_status")
    async def get_workflow_status(self) -> str:
        if self.workflowExecutor:
            return self.workflowExecutor.get_result()
        return "Workflow not started"